import { Router, Request, Response } from 'express';
import { DBOS } from "@dbos-inc/dbos-sdk";
import { ComplianceSystem } from '../workflows';
import { ComplianceDatabase } from '../database';
import { reportingQueue } from '../config/queues';

const router = Router();

// Generate compliance report
router.post('/generate', async (req: Request, res: Response): Promise<void> => {
  try {
    const { reportType } = req.body;

    if (!['monthly', 'quarterly', 'annual'].includes(reportType)) {
      res.status(400).json({ error: 'Invalid report type' });
      return;
    }

    // Start report generation workflow
    const handle = await DBOS.startWorkflow(
      ComplianceSystem,
      { queueName: reportingQueue.name }
    ).generateComplianceReport(reportType);

    res.json({
      workflowId: handle.workflowID,
      status: 'report_generation_started',
      message: `${reportType} compliance report generation initiated`
    });
  } catch (error) {
    DBOS.logger.error(`Error generating report: ${(error as Error).message}`);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get recent reports
router.get('/recent', async (_req: Request, res: Response): Promise<void> => {
  try {
    console.log('📄 Recent reports requested');

    // Get real recent reports from database
    const recentReports = await ComplianceDatabase.getRecentReports();

    res.json(recentReports);
  } catch (error) {
    console.error('Error fetching recent reports:', error);
    res.status(500).json({ error: 'Failed to fetch recent reports' });
  }
});

// Get report stats
router.get('/stats', (_req: Request, res: Response): void => {
  console.log('📈 Report stats requested');
  // TODO: Implement database-driven report stats
  res.status(501).json({ error: 'Report stats endpoint not yet implemented with database' });
});

export default router;
