// Test script to verify the /api/reports/generate endpoint fix
const testReportGeneration = async () => {
  const testCases = [
    {
      name: "Valid compliance-summary report",
      payload: {
        reportType: "compliance-summary",
        dateRange: {
          startDate: "2024-01-01T00:00:00.000Z",
          endDate: "2024-12-31T23:59:59.999Z"
        }
      },
      expectedStatus: 200
    },
    {
      name: "Valid kyc-status report",
      payload: {
        reportType: "kyc-status",
        dateRange: {
          startDate: "2024-06-01T00:00:00.000Z",
          endDate: "2024-06-30T23:59:59.999Z"
        }
      },
      expectedStatus: 200
    },
    {
      name: "Invalid report type (should return 400)",
      payload: {
        reportType: "invalid-type",
        dateRange: {
          startDate: "2024-01-01T00:00:00.000Z",
          endDate: "2024-12-31T23:59:59.999Z"
        }
      },
      expectedStatus: 400
    },
    {
      name: "Missing report type (should return 400)",
      payload: {
        dateRange: {
          startDate: "2024-01-01T00:00:00.000Z",
          endDate: "2024-12-31T23:59:59.999Z"
        }
      },
      expectedStatus: 400
    }
  ];

  console.log("🧪 Testing /api/reports/generate endpoint...\n");

  for (const testCase of testCases) {
    try {
      console.log(`Testing: ${testCase.name}`);
      console.log(`Payload:`, JSON.stringify(testCase.payload, null, 2));
      
      const response = await fetch('http://localhost:3000/api/reports/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testCase.payload),
      });

      const responseData = await response.json();
      
      console.log(`Status: ${response.status} (expected: ${testCase.expectedStatus})`);
      console.log(`Response:`, JSON.stringify(responseData, null, 2));
      
      if (response.status === testCase.expectedStatus) {
        console.log("✅ PASS\n");
      } else {
        console.log("❌ FAIL\n");
      }
    } catch (error) {
      console.log(`❌ ERROR: ${error.message}\n`);
    }
  }
};

// Run the tests
testReportGeneration().catch(console.error);
